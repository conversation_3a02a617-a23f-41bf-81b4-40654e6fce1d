<!-- Modern App Layout with Bottom Tabs -->
<div class="app-container">
  <!-- App Bar -->
  <mat-toolbar color="primary">
    <mat-icon>storage</mat-icon>
    <span>NS Drive</span>

    <span class="spacer"></span>

    <!-- Action Buttons -->
    <button
      mat-icon-button
      (click)="toggleDarkMode()"
      [matTooltip]="isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'"
    >
      <mat-icon>{{ isDarkMode ? "light_mode" : "dark_mode" }}</mat-icon>
    </button>

    <button mat-icon-button matTooltip="Help">
      <mat-icon>help</mat-icon>
    </button>
  </mat-toolbar>

  <!-- Main Content with Bottom Tabs -->
  <div class="content-container">
    <mat-tab-group
      [selectedIndex]="getSelectedTabIndex()"
      (selectedIndexChange)="onTabChange($event)"
      headerPosition="below"
      animationDuration="300ms"
    >
      <!-- Operations Tab -->
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon>dashboard</mat-icon>
          <span>Operations</span>
        </ng-template>
        <app-home></app-home>
      </mat-tab>

      <!-- Profiles Tab -->
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon>folder_shared</mat-icon>
          <span>Profiles</span>
        </ng-template>
        <app-profiles
          *ngIf="(navigationService.currentState$ | async)?.page === 'profiles'"
        ></app-profiles>
        <app-profile-edit
          *ngIf="
            (navigationService.currentState$ | async)?.page === 'profile-edit'
          "
        ></app-profile-edit>
      </mat-tab>

      <!-- Remotes Tab -->
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon>cloud_queue</mat-icon>
          <span>Remotes</span>
        </ng-template>
        <app-remotes></app-remotes>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>
